import React, { useEffect, useRef } from "react";
import { Dropdown, Switch } from "antd";
import { useMediaDeviceSelect } from "@livekit/components-react";
import AudioDeviceDropdown from "./AudioDeviceDropdown";
import SpeakerDeviceDropdown from "./SpeakerDeviceDropdown";
import { useNoiseSuppressionContext } from "../../context/indexContext";
import "./CombinedAudioDropdown.scss";

const CombinedAudioDropdown = React.memo(function CombinedAudioDropdown({
  micSelection = undefined,
  speakerSelection = undefined,
  onMicChange = undefined,
  onSpeakerChange = undefined,
  className = ""
}) {
  // Get noise suppression state from context
  const { isNoiseSuppressionEnabled, setIsNoiseSuppressionEnabled } = useNoiseSuppressionContext();

  // Get LiveKit device management hooks
  const { devices: audioInputDevices, activeDeviceId: activeMicId, setActiveMediaDevice: setActiveMicDevice } = useMediaDeviceSelect({ kind: "audioinput" });
  const { devices: audioOutputDevices, activeDeviceId: activeSpeakerId, setActiveMediaDevice: setActiveSpeakerDevice } = useMediaDeviceSelect({ kind: "audiooutput" });

  // Track if we've set initial devices (separate for each device type)
  const hasSetInitialMic = useRef(false);
  const hasSetInitialSpeaker = useRef(false);

  // Apply initial microphone device (ONLY ONCE for initial setup)
  useEffect(() => {
    if (hasSetInitialMic.current) return; // Only run once

    if (micSelection && micSelection !== activeMicId && audioInputDevices.length > 0) {
      const deviceExists = audioInputDevices.some(device => device.deviceId === micSelection);
      if (deviceExists) {
        setActiveMicDevice(micSelection).catch(error => {
          console.error('Failed to set initial mic device:', error);
        });
        hasSetInitialMic.current = true;
      }
    }
  }, [micSelection, activeMicId, setActiveMicDevice, audioInputDevices]);

  // Apply initial speaker device (ONLY ONCE for initial setup)
  useEffect(() => {
    if (hasSetInitialSpeaker.current) return; // Only run once

    if (speakerSelection && speakerSelection !== activeSpeakerId && audioOutputDevices.length > 0) {
      const deviceExists = audioOutputDevices.some(device => device.deviceId === speakerSelection);
      if (deviceExists) {
        setActiveSpeakerDevice(speakerSelection).catch(error => {
          console.error('Failed to set initial speaker device:', error);
        });
        hasSetInitialSpeaker.current = true;
      }
    }
  }, [speakerSelection, activeSpeakerId, setActiveSpeakerDevice, audioOutputDevices]);
  const dropdownContent = (
    <div className="combined-audio-dropdown-menu">
      <div className="audio-settings-row">
        {/* Microphone Section */}
        <div className="audio-setting-section">
          <AudioDeviceDropdown
            kind="audioinput"
            onActiveDeviceChange={onMicChange}
            initialSelection={micSelection}
            className="device-dropdown"
          />
        </div>
        <div className="audio-setting-section">
          <SpeakerDeviceDropdown
            kind="audiooutput"
            onActiveDeviceChange={onSpeakerChange}
            initialSelection={speakerSelection}
            className="device-dropdown"
          />
        </div>
      </div>

      {/* Noise Suppression Section */}
      <div className="noise-suppression-section">
        <div className="noise-suppression-label">
          <span>Noise Suppression</span>
          <small>Reduce background noise during calls</small>
        </div>
        <Switch
          checked={isNoiseSuppressionEnabled}
          onChange={(enabled) => {
            setIsNoiseSuppressionEnabled(enabled);
            // Note: This only controls advanced noise suppression
            // Basic noise cancellation is controlled in SettingsModal
          }}
          size="default"
        />
      </div>
    </div>
  );

  return (
    <Dropdown
      dropdownRender={() => dropdownContent}
      trigger={['click']}
      placement="top"
      className={className}
    >
      <button
        className="combined-audio-dropdown-button"
        type="button"
        aria-label="Audio settings - microphone and speaker selection"
        aria-haspopup="listbox"
        aria-expanded="false"
      >
        {/* Removed Unicode arrow - now using CSS-based arrow via ::after pseudo-element */}
      </button>
    </Dropdown>
  );
});





export default CombinedAudioDropdown;
