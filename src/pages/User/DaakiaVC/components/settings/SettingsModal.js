import { Mo<PERSON>, Switch, Ta<PERSON>, Slider, Select } from "antd";
import React from "react";
import { PiVideoCameraFill } from "react-icons/pi";
import { HiSpeakerWave } from "react-icons/hi2";
import { useMediaDeviceSelect } from "@livekit/components-react";

import "../../styles/SettingModal.scss";
import { ReactComponent as VoiceIcon } from "./icons/VoiceIco.svg";
import { useNoiseSuppressionContext } from "../../context/indexContext";


// import { SettingsMenuServices } from "../../services/SettingsMenuServices";

export default function SettingsModal({
  isSettingsModalOpen,
  setIsSettingsModalOpen,
  room,
  meetingId,
  isSelfVideoMirrored,
  setIsSelfVideoMirrored,
  brightness = 100,
  onBrightnessChange,
  // Volume props (following brightness pattern, no RPC needed)
  outputVolume = 100,
  onOutputVolumeChange,
  // Auto video off props (following volume pattern, no RPC needed)
  autoVideoOff = false,
  onAutoVideoOffChange,
  // Auto audio off props (following volume pattern, no RPC needed)
  autoAudioOff = false,
  onAutoAudioOffChange,
  // Speaker device props
  speakerDeviceId,
  setSpeakerDeviceId,
  // Microphone device props
  deviceIdAudio,
  setDeviceIdAudio,
  // meetingFeatures,
  // isHost,
  // isCoHost,
}) {

  const [noiseCancellation, setNoiseCancellation] = React.useState(
    room?.options?.audioCaptureDefaults?.noiseSuppression
  );

  // Add custom noise suppression context
  const {
    isNoiseSuppressionEnabled,
    setIsNoiseSuppressionEnabled,
    isBasicNoiseCancellationEnabled,
    setIsBasicNoiseCancellationEnabled
  } = useNoiseSuppressionContext();

  const [echoCancellation, setEchoCancellation] = React.useState(
    room?.options?.audioCaptureDefaults?.echoCancellation
  );

  // Speaker device selection
  const { devices: speakerDevices, activeDeviceId: activeSpeakerDeviceId, setActiveMediaDevice: setActiveSpeakerDevice } = useMediaDeviceSelect({
    kind: "audiooutput"
  });

  // Microphone device selection
  const { devices: micDevices, activeDeviceId: activeMicDeviceId, setActiveMediaDevice: setActiveMicDevice } = useMediaDeviceSelect({
    kind: "audioinput"
  });

  // Handle speaker device change
  const handleSpeakerDeviceChange = React.useCallback(async (deviceId) => {
    try {
      await setActiveSpeakerDevice(deviceId);
      setSpeakerDeviceId(deviceId);
    } catch (error) {
      console.error('Failed to switch speaker device:', error);
    }
  }, [setActiveSpeakerDevice, setSpeakerDeviceId]);

  // Handle microphone device change
  const handleMicDeviceChange = React.useCallback(async (deviceId) => {
    try {
      await setActiveMicDevice(deviceId);
      setDeviceIdAudio(deviceId);
    } catch (error) {
      console.error('Failed to switch microphone device:', error);
    }
  }, [setActiveMicDevice, setDeviceIdAudio]);

  // AI Agents state
  /* Commented out AI Agents state
  const [aiAgents, setAiAgents] = React.useState({
    basicAgent: false,
    complianceAgent: false,
    jiraAgent: false,
    calendarAgent: false,
    notetakerAgent: false
  });
  */

  // Agent configuration mapping
  /* Commented out Agent configuration
  const agentConfig = {
    basicAgent: {
      id: 'basic-agent',
      name: 'Assistant',
      displayName: 'Basic Assistant',
      description: 'AI agent that provides general assistance and responds to meeting conversations.'
    },
    complianceAgent: {
      id: 'compliance-agent',
      name: 'Compliance Bot',
      displayName: 'Compliance Bot',
      description: 'AI agent specialized in compliance monitoring and PCI DSS requirements.'
    },
    translationAgent: {
      id: 'translation-agent',
      name: 'Daakia Translator',
      displayName: 'Daakia Translator',
      description: 'AI agent that helps with translation in real time in the call.'
    },
    // calendarAgent: {
    //   id: 'calendar-agent',
    //   name: 'Calendar Bot',
    //   displayName: 'Calendar Bot',
    //   description: 'AI agent that assists with calendar management and scheduling.'
    // },
    // notetakerAgent: {
    //   id: 'notetaker-agent',
    //   name: 'Notetaker',
    //   displayName: 'Notetaker',
    //   description: 'AI agent that takes comprehensive meeting notes and summaries.'
    // }
  };
  */

  // Console log meeting ID when modal opens
  React.useEffect(() => {
    if (isSettingsModalOpen) {
      // console.log('Settings Modal opened - Meeting UID:', meetingId);
    }
  }, [isSettingsModalOpen, meetingId]);

  const handleNoiseCancellation = () => {
    setNoiseCancellation((prevNoiseCancellation) => {
      const newNoiseCancellation = !prevNoiseCancellation;

      // If turning ON basic noise cancellation, turn OFF advanced noise suppression
      if (newNoiseCancellation && isNoiseSuppressionEnabled) {
        setIsNoiseSuppressionEnabled(false);
      }

      // Update context state for basic noise cancellation
      setIsBasicNoiseCancellationEnabled(newNoiseCancellation);

      // Update room option for noise suppression
      if (room?.options?.audioCaptureDefaults) {
        room.options.audioCaptureDefaults.noiseSuppression =
          newNoiseCancellation;
      }
      return newNoiseCancellation;
    });
  };

  const handleAdvancedNoiseSuppression = () => {
    const newAdvancedNoiseSuppression = !isNoiseSuppressionEnabled;

    // If turning ON advanced noise suppression, turn OFF basic noise cancellation
    if (newAdvancedNoiseSuppression && noiseCancellation) {
      setNoiseCancellation(false);
      setIsBasicNoiseCancellationEnabled(false);
      // Also update room option for basic noise cancellation
      if (room?.options?.audioCaptureDefaults) {
        room.options.audioCaptureDefaults.noiseSuppression = false;
      }
    }

    setIsNoiseSuppressionEnabled(newAdvancedNoiseSuppression);
  };

  const handleEchoCancellation = () => {
    setEchoCancellation((prevEchoCancellation) => {
      const newEchoCancellation = !prevEchoCancellation;
      // Update room option for echo cancellation
      if (room?.options?.audioCaptureDefaults) {
        room.options.audioCaptureDefaults.echoCancellation =
          newEchoCancellation;
      }
      return newEchoCancellation;
    });
  };

  /* Commented out AI Agent handler
  const handleAiAgentToggle = async (agentKey) => {
    const newValue = !aiAgents[agentKey];
    const agent = agentConfig[agentKey];

    // If turning on a new agent, first turn off any active agent
    if (newValue) {
      // Find any active agent
      const activeAgentKey = Object.keys(aiAgents).find(key => 
        key !== agentKey && 
        !key.includes('_dispatch_id') && 
        aiAgents[key]
      );

      if (activeAgentKey) {
        // Turn off the active agent first
        const activeAgent = agentConfig[activeAgentKey];
        const activeDispatchId = aiAgents[`${activeAgentKey}_dispatch_id`];
        
        try {
          if (activeDispatchId) {
            await SettingsMenuServices.deDispatchAgentService(meetingId, activeDispatchId);
            // console.log(`${activeAgent.displayName} de-dispatched successfully`);
          }
        } catch (error) {
          console.error(`Failed to de-dispatch ${activeAgent.displayName}:`, error);
          return; // Don't proceed with new agent if de-dispatch fails
        }

        // Update state to turn off the active agent
        setAiAgents((prevAgents) => ({
          ...prevAgents,
          [activeAgentKey]: false
        }));
      }
    }

    // Update state for the new agent
    setAiAgents((prevAgents) => ({
      ...prevAgents,
      [agentKey]: newValue
    }));

    try {
      if (newValue && agent) {
        // Call API if agent is being enabled
        const response = await SettingsMenuServices.dispatchAgentService(meetingId, agent.id, agent.name);
        // console.log(`${agent.displayName} dispatched successfully`);
        // Store dispatch_id in state for later use
        setAiAgents((prevAgents) => ({
          ...prevAgents,
          [`${agentKey}_dispatch_id`]: response.data.dispatch_id
        }));
      } else if (!newValue && agent) {
        // Call API if agent is being disabled
        const dispatchId = aiAgents[`${agentKey}_dispatch_id`];
        if (dispatchId) {
          await SettingsMenuServices.deDispatchAgentService(meetingId, dispatchId);
          // console.log(`${agent.displayName} de-dispatched successfully`);
        }
      }
    } catch (error) {
      console.error(`Failed to ${newValue ? 'dispatch' : 'de-dispatch'} ${agent.displayName}:`, error);
      // Revert state on error
      setAiAgents((prevAgents) => ({
        ...prevAgents,
        [agentKey]: !newValue
      }));
    }
  };
  */

  const items = [
    {
      key: "1",
      label: (
        <div className="tab-label">
          <VoiceIcon />
          <span>Voice</span>
        </div>
      ),
      children: (
        <div className="voice-settings">
          <h2>Voice Settings</h2>
          <div className="voice-settings-options">
            <div>
              <div className="voice-settings-options-text">
                <p>Noise cancellation</p>
                <span>
                  Remove background noise from your audio input to improve the
                  call quality.
                </span>
              </div>
              <Switch
                checked={noiseCancellation}
                onChange={handleNoiseCancellation}
              />
            </div>
            <div>
              <div className="voice-settings-options-text">
                <p>Advanced noise suppression</p>
                <span>
                  Enhanced AI-powered noise suppression for better audio quality.
                </span>
              </div>
              <Switch
                checked={isNoiseSuppressionEnabled}
                onChange={handleAdvancedNoiseSuppression}
              />
            </div>
            <div>
              <div className="voice-settings-options-text">
                <p>Echo cancellation</p>
                <span>
                  Remove sound echo from your audio input to improve the call
                  quality.
                </span>
              </div>
              <Switch
                checked={echoCancellation}
                onChange={handleEchoCancellation}
              />
            </div>
            <div className="voice-settings-options-container">
              <div className="voice-settings-options-texts">
                <p>Output volume</p>
                <span>
                  Adjust the output volume for speakers and audio playback.
                </span>
              </div>
              <div className="brightness-slider-container">
                <Slider
                  min={0}
                  max={100}
                  value={outputVolume}
                  onChange={onOutputVolumeChange}
                  tooltip={{ formatter: (value) => `${value}%` }}
                  style={{ width: '200px' }}
                />
                <span className="brightness-value">{outputVolume}%</span>
              </div>
            </div>
            <div>
              <div className="voice-settings-options-text">
                <p>Auto-mute on joining meeting</p>
                <span>
                  Automatically mute your microphone when joining the meeting.
                </span>
              </div>
              <Switch
                checked={autoAudioOff}
                onChange={onAutoAudioOffChange}
              />
            </div>
          </div>
        </div>
      ),
    },
    {
      key: "2",
      label: (
        <div className="tab-label">
          <PiVideoCameraFill />
          <span>Video</span>
        </div>
      ),
      children: (
        <div className="voice-settings">
          <h2>Video Settings</h2>
          <div className="voice-settings-options">
            <div>
              <div className="voice-settings-options-text">
                <p>Mirror video</p>
                <span>
                  Flip your video horizontally to correct the orientation.
                </span>
              </div>
              <Switch
                checked={isSelfVideoMirrored}
                onChange={() => setIsSelfVideoMirrored((prev) => !prev)}
              />
            </div>
            <div className="voice-settings-options-container">
              <div className="voice-settings-options-text">
                <p>Video brightness</p>
                <span>
                  Adjust the brightness of your video to improve visibility.
                </span>
              </div>
              <div className="brightness-slider-container">
                <Slider
                  min={50}
                  max={150}
                  value={brightness}
                  onChange={onBrightnessChange}
                  tooltip={{ formatter: (value) => `${value}%` }}
                  style={{ width: '200px' }}
                />
                <span className="brightness-value">{brightness}%</span>
              </div>
            </div>
            <div>
              <div className="voice-settings-options-text">
                <p>Auto video off on joining meeting</p>
                <span>
                  Automatically turn off your camera when joining the meeting.
                </span>
              </div>
              <Switch
                checked={autoVideoOff}
                onChange={onAutoVideoOffChange}
              />
            </div>
          </div>
        </div>
      ),
    },
    {
      key: "3",
      label: (
        <div className="tab-label">
          <HiSpeakerWave />
          <span>Devices</span>
        </div>
      ),
      children: (
        <div className="voice-settings">
          <h2>Device Settings</h2>
          <div className="voice-settings-options">
            <div className="voice-settings-options-container">
              <div className="voice-settings-options-text">
                <p>Microphone Device</p>
                <span>
                  Select the audio input device for microphone and voice recording.
                </span>
              </div>
              <div className="device-select-container">
                <Select
                  value={deviceIdAudio || activeMicDeviceId}
                  onChange={handleMicDeviceChange}
                  style={{ width: '250px' }}
                  placeholder="Select microphone device"
                  options={micDevices.map(device => ({
                    value: device.deviceId,
                    label: device.label || `Microphone ${device.deviceId.slice(0, 8)}...`
                  }))}
                />
              </div>
            </div>
            <div className="voice-settings-options-container">
              <div className="voice-settings-options-text">
                <p>Speaker Device</p>
                <span>
                  Select the audio output device for speakers and audio playback.
                </span>
              </div>
              <div className="device-select-container">
                <Select
                  value={speakerDeviceId || activeSpeakerDeviceId}
                  onChange={handleSpeakerDeviceChange}
                  style={{ width: '250px' }}
                  placeholder="Select speaker device"
                  options={speakerDevices.map(device => ({
                    value: device.deviceId,
                    label: device.label || `Speaker ${device.deviceId.slice(0, 8)}...`
                  }))}
                />
              </div>
            </div>
          </div>
        </div>
      ),
    }
    /* Commented out AI Agents tab
    ...((isHost || isCoHost) && meetingFeatures?.subscription_id === 49 ? [{
      key: "3",
      label: (
        <div className="tab-label">
          <PiRobotFill />
          <span>AI Agents</span>
        </div>
      ),
      children: (
        <div className="voice-settings">
          <h2>AI Agents</h2>
          <div className="voice-settings-options">
            {Object.entries(agentConfig).map(([agentKey, agent]) => (
              <div key={agentKey}>
                <div className="voice-settings-options-text">
                  <p>{agent.displayName}</p>
                  <span>{agent.description}</span>
                </div>
                <Switch
                  checked={aiAgents[agentKey]}
                  onChange={() => handleAiAgentToggle(agentKey)}
                />
              </div>
            ))}
          </div>
        </div>
      ),
    }] : [])
    */
  ];

  return (
    <Modal
      className="settings-modal"
      title="Settings"
      open={isSettingsModalOpen}
      onOk={() => setIsSettingsModalOpen(false)}
      onCancel={() => setIsSettingsModalOpen(false)}
      onClose={() => setIsSettingsModalOpen(false)}
    >
      <h3 className="ant-modal-title">Settings</h3>
      <Tabs defaultActiveKey="1" items={items} tabPosition="left" className="settings-tabs" />
    </Modal>
  );
}
