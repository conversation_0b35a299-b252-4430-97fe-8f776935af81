import React, { createContext, useContext, useState, useMemo, useRef } from 'react'

const VirtualBackgroundContext = createContext()
export function VirtualBackgroundProvider({ children }) {
  const [currentEffect, setCurrentEffect] = useState(null)
  const isApplyingEffect = useRef(false) // Prevent multiple simultaneous applications

  const contextValue = useMemo(() => ({
    currentEffect,
    setCurrentEffect,
    isApplyingEffect,
  }), [currentEffect])

  return (
    <VirtualBackgroundContext.Provider value={contextValue}>
      {children}
    </VirtualBackgroundContext.Provider>
  )
}

const NoiseSuppressionContext = createContext()
export function NoiseSuppressionProvider({ children }) {
  const [isNoiseSuppressionEnabled, setIsNoiseSuppressionEnabled] = useState(false)
  const [isBasicNoiseCancellationEnabled, setIsBasicNoiseCancellationEnabled] = useState(false)

  const contextValue = useMemo(() => ({
    isNoiseSuppressionEnabled,
    setIsNoiseSuppressionEnabled,
    isBasicNoiseCancellationEnabled,
    setIsBasicNoiseCancellationEnabled,
  }), [isNoiseSuppressionEnabled, isBasicNoiseCancellationEnabled])

  return (
    <NoiseSuppressionContext.Provider value={contextValue}>
      {children}
    </NoiseSuppressionContext.Provider>
  )
}

export function IndexProvider({ children }) {
  return (
    <VirtualBackgroundProvider>
      <NoiseSuppressionProvider>
        {children}
      </NoiseSuppressionProvider>
    </VirtualBackgroundProvider>
  )
}

export function useVirtualBackground() {
  const context = useContext(VirtualBackgroundContext)
  if (!context) throw new Error('useVirtualBackground must be used within VirtualBackgroundProvider')
  return context
}

export function useNoiseSuppressionContext() {
  const context = useContext(NoiseSuppressionContext)
  if (!context) throw new Error('useNoiseSuppressionContext must be used within NoiseSuppressionProvider')
  return context
}



export default VirtualBackgroundContext